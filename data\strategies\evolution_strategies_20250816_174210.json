[{"strategy_id": "crossover_f76fd024", "name": "Strategy LP_NIFTY_24300_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24300_20250718192943 and Strategy LC_NIFTY_24400_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_a46cda50", "name": "Strategy LP_NIFTY_25100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25100_20250718192943 and Strategy LST_NIFTY_25100_24000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_f1d9aa97", "name": "Strategy LP_NIFTY_24100_20250718192943", "description": "Crossover of Strategy LP_NIFTY_24100_20250718192943 and Strategy LST_NIFTY_25300_24200_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.019782823959018522, "take_profit": 0.053941309480465875}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}]