#!/usr/bin/env python3
"""
[INIT] CENTRALIZED MAIN ENTRY POINT FOR NIFTY & BANK NIFTY OPTIONS TRADING SYSTEM
═══════════════════════════════════════════════════════════════════════════════

This is the unified entry point for all options trading system agents and workflows.
Supports both individual agent execution and complete workflow orchestration.

Features:
[TARGET] Individual agent execution with custom configurations
[WOR<PERSON>FLOW] Complete workflow orchestration with dependency management
[FAST] GPU optimization and performance monitoring for options
[STATUS] Real-time status monitoring and health checks
[SECURITY] Error handling and graceful shutdown
[METRICS] Performance metrics and logging
[OPTIONS] Specialized for NIFTY & BANK NIFTY options only
[TIMEFRAMES] Multi-timeframe support: 1min→3min,5min,15min data processing
[PAPER] Paper trading with virtual Rs. 1,00,000 balance
[REAL] Real trading with SmartAPI integration
"""

import asyncio
import argparse
import sys
import logging
import signal
import os
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime
import json

# Set environment variable to handle timezone parsing issues globally
os.environ['POLARS_IGNORE_TIMEZONE_PARSE_ERROR'] = '1'

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import options agents
from agents.options_data_ingestion_agent import OptionsDataIngestionAgent
from agents.options_feature_engineering_agent import OptionsFeatureEngineeringAgent
from agents.options_strategy_generation_agent import OptionsStrategyGenerationAgent
from agents.options_backtesting_agent import OptionsBacktestingAgent
from agents.options_ai_training_agent import OptionsAITrainingAgent
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent
from agents.options_signal_generation_agent import OptionsSignalGenerationAgent
from agents.options_risk_management_agent import OptionsRiskManagementAgent
from agents.options_execution_agent import OptionsExecutionAgent
from agents.options_performance_analysis_agent import OptionsPerformanceAnalysisAgent
from agents.options_llm_interface_agent import OptionsLLMInterfaceAgent
from agents.options_strategy_evolution_agent import OptionsStrategyEvolutionAgent
from agents.system_status_monitor import SystemStatusMonitor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/options_main.log'),
        logging.StreamHandler(sys.stdout) # Explicitly set stream to sys.stdout
    ],
    force=True # Force re-configuration of logging
)
logger = logging.getLogger(__name__)

class VirtualAccount:
    """Virtual trading account for paper trading"""
    
    def __init__(self, initial_balance: float = 100000.0):
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.positions = {}
        self.trades = []
        self.pnl = 0.0
        self.created_at = datetime.now()
        
    def get_balance(self) -> float:
        """Get current account balance"""
        return self.balance
    
    def get_pnl(self) -> float:
        """Get current P&L"""
        return self.pnl
    
    def place_order(self, symbol: str, quantity: int, price: float, order_type: str) -> Dict[str, Any]:
        """Place a virtual order"""
        order_id = f"PAPER_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.trades)}"
        
        if order_type.upper() == 'BUY':
            cost = quantity * price
            if cost <= self.balance:
                self.balance -= cost
                if symbol in self.positions:
                    self.positions[symbol] += quantity
                else:
                    self.positions[symbol] = quantity
                
                trade = {
                    'order_id': order_id,
                    'symbol': symbol,
                    'quantity': quantity,
                    'price': price,
                    'order_type': order_type,
                    'timestamp': datetime.now(),
                    'status': 'FILLED'
                }
                self.trades.append(trade)
                logger.info(f"[PAPER] BUY order filled: {symbol} x {quantity} @ {price}")
                return trade
            else:
                logger.warning(f"[PAPER] Insufficient balance for BUY order: {symbol}")
                return {'status': 'REJECTED', 'reason': 'Insufficient balance'}
        
        elif order_type.upper() == 'SELL':
            if symbol in self.positions and self.positions[symbol] >= quantity:
                self.balance += quantity * price
                self.positions[symbol] -= quantity
                if self.positions[symbol] == 0:
                    del self.positions[symbol]
                
                trade = {
                    'order_id': order_id,
                    'symbol': symbol,
                    'quantity': quantity,
                    'price': price,
                    'order_type': order_type,
                    'timestamp': datetime.now(),
                    'status': 'FILLED'
                }
                self.trades.append(trade)
                logger.info(f"[PAPER] SELL order filled: {symbol} x {quantity} @ {price}")
                return trade
            else:
                logger.warning(f"[PAPER] Insufficient position for SELL order: {symbol}")
                return {'status': 'REJECTED', 'reason': 'Insufficient position'}
    
    def get_positions(self) -> Dict[str, int]:
        """Get current positions"""
        return self.positions.copy()
    
    def get_trades(self) -> List[Dict[str, Any]]:
        """Get all trades"""
        return self.trades.copy()
    
    def save_state(self, filepath: str):
        """Save account state to file"""
        state = {
            'initial_balance': self.initial_balance,
            'balance': self.balance,
            'positions': self.positions,
            'trades': [
                {
                    **trade,
                    'timestamp': trade['timestamp'].isoformat()
                } for trade in self.trades
            ],
            'pnl': self.pnl,
            'created_at': self.created_at.isoformat()
        }
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(state, f, indent=2)
        
        logger.info(f"[PAPER] Account state saved to {filepath}")

class OptionsSystemOrchestrator:
    """
    Main orchestrator for the Options Trading System
    
    Manages all 12 agents and their workflows:
    1. Data Ingestion Agent
    2. Feature Engineering Agent  
    3. Strategy Generation Agent
    4. Backtesting Agent
    5. AI Training Agent
    6. Market Monitoring Agent
    7. Signal Generation Agent
    8. Risk Management Agent
    9. Execution Agent
    10. Performance Analysis Agent
    11. LLM Interface Agent
    12. Strategy Evolution Agent
    """
    
    def __init__(self):
        """Initialize the Options System Orchestrator"""
        self.agents = {}
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        self.virtual_account = None
        self.trading_mode = 'demo'  # demo, paper, real
        self.system_status_monitor: Optional[SystemStatusMonitor] = None
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("[INIT] Options System Orchestrator initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"[SIGNAL] Received signal {signum}, initiating shutdown...")
        self.shutdown_event.set()
    
    def set_trading_mode(self, mode: str):
        """Set trading mode: demo, paper, or real"""
        self.trading_mode = mode
        if mode == 'paper':
            self.virtual_account = VirtualAccount(initial_balance=100000.0)
            logger.info("[PAPER] Virtual account created with Rs. 1,00,000 balance")
        elif mode == 'real':
            logger.info("[REAL] Real trading mode enabled - SmartAPI integration active")
        else:
            logger.info("[DEMO] Demo mode enabled - no real trades")
    
    async def run_agent(self, agent_name: str, config_path: Optional[str] = None, **kwargs) -> bool:
        """Run a specific options trading agent"""
        try:
            logger.info(f"[AGENT] Starting {agent_name} agent in {self.trading_mode.upper()} mode...")
            
            # Agent mapping with their classes
            agent_classes = {
                'data_ingestion': OptionsDataIngestionAgent,
                'feature_engineering': OptionsFeatureEngineeringAgent,
                'strategy_generation': OptionsStrategyGenerationAgent,
                'backtesting': OptionsBacktestingAgent,
                'ai_training': OptionsAITrainingAgent,
                'market_monitoring': OptionsMarketMonitoringAgent,
                'signal_generation': OptionsSignalGenerationAgent,
                'risk_management': OptionsRiskManagementAgent,
                'execution': OptionsExecutionAgent,
                'performance_analysis': OptionsPerformanceAnalysisAgent,
                'llm_interface': OptionsLLMInterfaceAgent,
                'strategy_evolution': OptionsStrategyEvolutionAgent,
                'system_status': SystemStatusMonitor
            }
            
            if agent_name not in agent_classes:
                logger.error(f"[ERROR] Unknown agent: {agent_name}")
                return False
            
            # Initialize agent
            agent_class = agent_classes[agent_name]
            if config_path:
                agent = agent_class(config_path=config_path)
            else:
                agent = agent_class()
            
            # Pass trading mode and virtual account to agent
            kwargs.update({
                'trading_mode': self.trading_mode,
                'virtual_account': self.virtual_account
            })
            
            # Store agent reference
            self.agents[agent_name] = agent
            
            # Filter kwargs to remove parameters that agents don't accept
            agent_kwargs = {k: v for k, v in kwargs.items() if k not in ['demo', 'monitor']}
            
            # Initialize and start agent
            await agent.initialize(**agent_kwargs)
            success = await agent.start(**agent_kwargs)
            
            if success:
                logger.info(f"[SUCCESS] {agent_name} agent started successfully")
            else:
                logger.error(f"[ERROR] {agent_name} agent failed to start")
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Error running {agent_name} agent: {e}")
            return False
    
    async def run_workflow(self, workflow_name: str, **kwargs) -> bool:
        """Run a complete options trading workflow"""
        try:
            logger.info(f"[WORKFLOW] Starting {workflow_name} workflow in {self.trading_mode.upper()} mode...")
            
            workflows = {
                'full_pipeline': self._workflow_full_pipeline,
                'training_pipeline': self._workflow_training_pipeline,
                'live_trading': self._workflow_live_trading,
                'data_pipeline': self._workflow_data_pipeline,
                'strategy_development': self._workflow_strategy_development,
                'options_research': self._workflow_options_research,
                'multi_timeframe_analysis': self._workflow_multi_timeframe_analysis
            }
            
            if workflow_name not in workflows:
                logger.error(f"[ERROR] Unknown workflow: {workflow_name}")
                return False
            
            success = await workflows[workflow_name](**kwargs)
            
            if success:
                logger.info(f"[SUCCESS] {workflow_name} workflow completed successfully")
                # Save virtual account state if in paper mode
                if self.trading_mode == 'paper' and self.virtual_account:
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    self.virtual_account.save_state(f'data/paper_trading/account_state_{timestamp}.json')
            else:
                logger.error(f"[ERROR] {workflow_name} workflow failed")
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Error running {workflow_name} workflow: {e}")
            return False
    
    async def _workflow_full_pipeline(self, **kwargs) -> bool:
        """Complete end-to-end options trading pipeline"""
        try:
            logger.info("[WORKFLOW] Starting full options pipeline workflow...")

            # Step 1: Data Ingestion
            if not await self.run_agent('data_ingestion', **kwargs):
                return False

            # Step 2: Feature Engineering
            if not await self.run_agent('feature_engineering', **kwargs):
                return False

            # Step 3: Strategy Generation
            if not await self.run_agent('strategy_generation', **kwargs):
                return False

            # Step 4: Backtesting
            if not await self.run_agent('backtesting', **kwargs):
                return False

            # Step 5: AI Training
            if not await self.run_agent('ai_training', **kwargs):
                return False

            # Step 6: Start live trading agents
            await asyncio.gather(
                self.run_agent('market_monitoring', **kwargs),
                self.run_agent('signal_generation', **kwargs),
                self.run_agent('risk_management', **kwargs),
                self.run_agent('execution', **kwargs),
                self.run_agent('performance_analysis', **kwargs)
            )

            return True

        except Exception as e:
            logger.error(f"[ERROR] Full pipeline workflow failed: {e}")
            return False
    
    async def _workflow_live_trading(self, **kwargs) -> bool:
        """Live options trading workflow with enhanced real-time capabilities"""
        try:
            logger.info(f"[WORKFLOW] Starting live options trading workflow in {self.trading_mode.upper()} mode...")

            # Step 1: Download historical data for last 8 days
            logger.info("[STEP 1] Downloading historical data for last 8 days...")
            if not await self._download_live_trading_historical_data(**kwargs):
                logger.error("[ERROR] Failed to download historical data")
                return False

            # Step 2: Generate multi-timeframe data (3min, 5min, 15min from 1min)
            logger.info("[STEP 2] Generating multi-timeframe data...")
            if not await self._generate_live_trading_timeframes(**kwargs):
                logger.error("[ERROR] Failed to generate multi-timeframe data")
                return False

            # Step 3: Load latest trained ML models
            logger.info("[STEP 3] Loading latest trained ML models...")
            if not await self._load_ml_models_for_live_trading(**kwargs):
                logger.error("[ERROR] Failed to load ML models")
                return False

            # Step 4: Start real-time data subscription
            logger.info("[STEP 4] Starting real-time data subscription...")
            if not await self._start_realtime_subscription(**kwargs):
                logger.error("[ERROR] Failed to start real-time subscription")
                return False

            # Step 5: Initialize and start system status monitor
            logger.info("[STEP 5] Initializing system status monitor...")
            self.system_status_monitor = SystemStatusMonitor()
            await self.system_status_monitor.initialize(
                initial_balance=kwargs.get('initial_balance', 100000.0),
                daily_trades_target=kwargs.get('daily_trades_target', 50),
                trading_mode=self.trading_mode
            )
            await self.system_status_monitor.start()

            # Step 6: Start all required trading agents
            logger.info("[STEP 6] Starting all required trading agents...")
            tasks = [
                # Market Monitoring Agent - Real-time data processing
                self.run_agent('market_monitoring', **kwargs),

                # Signal Generation Agent - 10-second interval signal generation
                self.run_agent('signal_generation', **kwargs),

                # Risk Management Agent - Entry/exit/hold decisions
                self.run_agent('risk_management', **kwargs),

                # Execution Agent - Smart order execution
                self.run_agent('execution', **kwargs),

                # Performance Analysis Agent - Real-time P&L tracking
                self.run_agent('performance_analysis', **kwargs)
            ]

            # Wait for all agents to start
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check if any agent failed
            for i, result in enumerate(results):
                if isinstance(result, Exception) or not result:
                    logger.error(f"[ERROR] Live trading agent {i} failed: {result}")
                    return False

            # Keep the workflow running until shutdown signal
            logger.info("[WORKFLOW] Live trading workflow is running. Press Ctrl+C to stop.")
            await self.shutdown_event.wait()

            return True

        except Exception as e:
            logger.error(f"[ERROR] Live trading workflow failed: {e}")
            return False

    async def _download_live_trading_historical_data(self, **kwargs) -> bool:
        """Download historical data for live trading (last 8 days)"""
        try:
            from datetime import datetime, timedelta

            # Calculate date range for last 8 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=8)

            # Set date range for data ingestion agent
            kwargs.update({
                'from_date': start_date.strftime("%Y-%m-%d %H:%M"),
                'to_date': end_date.strftime("%Y-%m-%d %H:%M"),
                'live_trading_mode': True,
                'strike_range_nifty': 500,  # ±500 strikes for NIFTY
                'strike_range_banknifty': 1000  # ±1000 strikes for BANKNIFTY
            })

            logger.info(f"[LIVE] Downloading historical data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

            # Run data ingestion agent for historical download
            return await self.run_agent('data_ingestion', **kwargs)

        except Exception as e:
            logger.error(f"[ERROR] Failed to download live trading historical data: {e}")
            return False

    async def _generate_live_trading_timeframes(self, **kwargs) -> bool:
        """Generate multi-timeframe data for live trading"""
        try:
            logger.info("[LIVE] Generating 3min, 5min, 15min timeframes from 1min data...")

            # The data ingestion agent should handle this automatically
            # This is a placeholder for any additional processing needed
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate live trading timeframes: {e}")
            return False

    async def _load_ml_models_for_live_trading(self, **kwargs) -> bool:
        """Load latest trained ML models for live trading"""
        try:
            logger.info("[LIVE] Loading latest trained ML models...")

            # Check if models directory exists
            models_path = Path("data/models")
            if not models_path.exists():
                logger.warning("[WARNING] No models directory found, continuing without ML models")
                return True

            # Find latest model files
            model_files = list(models_path.glob("*.pkl")) + list(models_path.glob("*.joblib"))
            if not model_files:
                logger.warning("[WARNING] No trained models found, continuing without ML models")
                return True

            logger.info(f"[LIVE] Found {len(model_files)} trained models")
            kwargs['ml_models_loaded'] = True

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to load ML models: {e}")
            return False

    async def _start_realtime_subscription(self, **kwargs) -> bool:
        """Start real-time subscription to required strikes"""
        try:
            logger.info("[LIVE] Starting real-time subscription to ±500 NIFTY and ±1000 BANKNIFTY strikes...")

            # This will be handled by the market monitoring agent
            # Set subscription parameters
            kwargs.update({
                'realtime_subscription': True,
                'nifty_strike_range': 500,
                'banknifty_strike_range': 1000
            })

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start real-time subscription: {e}")
            return False

    async def _workflow_training_pipeline(self, **kwargs) -> bool:
        """Enhanced Options AI training pipeline with date range input"""
        try:
            logger.info("[WORKFLOW] Starting enhanced options training pipeline...")

            # For training pipeline, use real strategy generation mode (not demo)
            original_mode = self.trading_mode
            self.set_trading_mode('real')  # Use real mode for proper training data generation
            logger.info("[TRAINING] Training pipeline using real strategy generation mode")

            # Only get date range from user input if not skipping historical data
            if not kwargs.get('skip_historical', False):
                # Get date range from user input
                from_date, to_date = self._get_date_range_input()
                
                # Pass date range to agents
                kwargs.update({
                    'from_date': from_date,
                    'to_date': to_date
                })

                # Step 1: Enhanced Data Ingestion with historical data download
                logger.info("[STEP 1] Enhanced data ingestion with historical data download...")
                if not await self.run_agent('data_ingestion', **kwargs):
                    logger.error("[ERROR] Data ingestion failed")
                    return False
            else:
                logger.info("[STEP 1] Skipping historical data download (using existing data)...")
                # Set default date range for agents that might need it
                kwargs.update({
                    'from_date': '2024-01-01 00:00',
                    'to_date': '2024-12-31 23:59',
                    'skip_historical': True
                })

            # Step 2: Multi-timeframe feature engineering
            logger.info("[STEP 2] Multi-timeframe feature engineering...")
            if not await self.run_agent('feature_engineering', **kwargs):
                logger.error("[ERROR] Feature engineering failed")
                return False

            # Step 3: Load strategies from YAML configuration
            logger.info("[STEP 3] Loading strategies from YAML configuration...")
            if not await self._load_strategies_from_yaml(**kwargs):
                logger.error("[ERROR] Strategy loading from YAML failed")
                return False

            # Step 4: Comprehensive backtesting
            logger.info("[STEP 4] Comprehensive backtesting...")
            if not await self.run_agent('backtesting', **kwargs):
                logger.error("[ERROR] Backtesting failed")
                return False

            # Step 5: AI training with enhanced features (incremental by default)
            logger.info("[STEP 5] AI training with enhanced features (incremental mode enabled)...")
            # Enable incremental training by default for training pipeline
            kwargs['incremental'] = kwargs.get('incremental', True)
            if not await self.run_agent('ai_training', **kwargs):
                logger.error("[ERROR] AI training failed")
                return False

            # Step 6: Strategy evolution
            logger.info("[STEP 6] Strategy evolution...")
            if not await self.run_agent('strategy_evolution', **kwargs):
                logger.error("[ERROR] Strategy evolution failed")
                return False

            logger.info("[SUCCESS] Enhanced training pipeline completed successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Enhanced training pipeline workflow failed: {e}")
            return False
        finally:
            # Restore original trading mode
            self.set_trading_mode(original_mode)
            logger.info(f"[TRAINING] Restored trading mode to {original_mode}")

    async def _load_strategies_from_yaml(self, **kwargs) -> bool:
        """Load strategies from YAML configuration file instead of generating new ones"""
        try:
            import yaml
            from pathlib import Path

            # Path to the strategies YAML file
            strategies_file = Path("config/options_strategies.yaml")

            if not strategies_file.exists():
                logger.error(f"[ERROR] Strategies YAML file not found: {strategies_file}")
                return False

            # Load strategies from YAML
            with open(strategies_file, 'r') as f:
                strategies_config = yaml.safe_load(f)

            logger.info(f"[YAML] Loaded strategies configuration from {strategies_file}")

            # Create strategies directory if it doesn't exist
            strategies_dir = Path("data/strategies")
            strategies_dir.mkdir(parents=True, exist_ok=True)

            # Convert YAML strategies to the format expected by backtesting
            strategies_data = []

            if 'strategies' in strategies_config:
                for strategy_name, strategy_config in strategies_config['strategies'].items():
                    strategy_data = {
                        'strategy_name': strategy_name,
                        'strategy_type': strategy_config.get('type', 'unknown'),
                        'description': strategy_config.get('description', ''),
                        'parameters': strategy_config.get('parameters', {}),
                        'entry_conditions': strategy_config.get('entry_conditions', {}),
                        'exit_conditions': strategy_config.get('exit_conditions', {}),
                        'risk_management': strategy_config.get('risk_management', {}),
                        'timeframes': strategy_config.get('timeframes', ['1min', '3min', '5min', '15min']),
                        'underlyings': strategy_config.get('underlyings', ['NIFTY', 'BANKNIFTY']),
                        'source': 'yaml_config'
                    }
                    strategies_data.append(strategy_data)
                    logger.info(f"[YAML] Loaded strategy: {strategy_name} ({strategy_config.get('type', 'unknown')})")

            if not strategies_data:
                logger.error("[ERROR] No strategies found in YAML configuration")
                return False

            # Save strategies in the format expected by backtesting agent
            import polars as pl
            strategies_df = pl.DataFrame(strategies_data)

            # Save to parquet file for backtesting agent
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            strategies_file_path = strategies_dir / f"strategies_from_yaml_{timestamp}.parquet"
            strategies_df.write_parquet(strategies_file_path)

            logger.info(f"[SUCCESS] Loaded {len(strategies_data)} strategies from YAML and saved to {strategies_file_path}")

            # Store the strategies file path for backtesting agent
            kwargs['strategies_file'] = str(strategies_file_path)

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to load strategies from YAML: {e}")
            return False

    def _get_date_range_input(self) -> Tuple[str, str]:
        """Get date range input from user"""
        try:
            print("\n" + "="*60)
            print("📅 HISTORICAL DATA DOWNLOAD CONFIGURATION")
            print("="*60)
            
            # Get from date
            while True:
                from_date_input = input("Enter FROM date (YYYY-MM-DD) [e.g., 2024-01-01]: ").strip()
                try:
                    from_date_obj = datetime.strptime(from_date_input, "%Y-%m-%d")
                    from_date = from_date_obj.strftime("%Y-%m-%d %H:%M")
                    break
                except ValueError:
                    print("❌ Invalid date format. Please use YYYY-MM-DD format.")
            
            # Get to date
            while True:
                to_date_input = input("Enter TO date (YYYY-MM-DD) [e.g., 2024-12-31]: ").strip()
                try:
                    to_date_obj = datetime.strptime(to_date_input, "%Y-%m-%d")
                    # Ensure to_date is after from_date
                    if to_date_obj <= from_date_obj:
                        print("❌ TO date must be after FROM date.")
                        continue
                    to_date = to_date_obj.strftime("%Y-%m-%d %H:%M")
                    break
                except ValueError:
                    print("❌ Invalid date format. Please use YYYY-MM-DD format.")
            
            print(f"\n✅ Date range configured:")
            print(f"   FROM: {from_date}")
            print(f"   TO: {to_date}")
            print("="*60)
            
            return from_date, to_date
            
        except KeyboardInterrupt:
            print("\n❌ Date range input cancelled by user")
            sys.exit(1)
        except Exception as e:
            logger.error(f"[ERROR] Error getting date range input: {e}")
            sys.exit(1)
    
    async def _workflow_data_pipeline(self, **kwargs) -> bool:
        """Data ingestion and feature engineering pipeline"""
        try:
            logger.info("[WORKFLOW] Starting data pipeline workflow...")

            if not await self.run_agent('data_ingestion', **kwargs):
                return False

            if not await self.run_agent('feature_engineering', **kwargs):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Data pipeline workflow failed: {e}")
            return False
    
    async def _workflow_strategy_development(self, **kwargs) -> bool:
        """Strategy development and backtesting workflow"""
        try:
            logger.info("[WORKFLOW] Starting strategy development workflow...")

            if not await self.run_agent('strategy_generation', **kwargs):
                return False

            if not await self.run_agent('backtesting', **kwargs):
                return False

            if not await self.run_agent('strategy_evolution', **kwargs):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Strategy development workflow failed: {e}")
            return False
    
    async def _workflow_options_research(self, **kwargs) -> bool:
        """Options research and analysis workflow"""
        try:
            logger.info("[WORKFLOW] Starting options research workflow...")

            if not await self.run_agent('data_ingestion', **kwargs):
                return False

            if not await self.run_agent('feature_engineering', **kwargs):
                return False

            if not await self.run_agent('performance_analysis', **kwargs):
                return False

            if not await self.run_agent('llm_interface', **kwargs):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Options research workflow failed: {e}")
            return False

    async def _workflow_multi_timeframe_analysis(self, **kwargs) -> bool:
        """Multi-timeframe options analysis workflow"""
        try:
            logger.info("[WORKFLOW] Starting multi-timeframe options analysis...")

            # Step 1: Download 1-minute data and generate multi-timeframes
            logger.info("[STEP 1] Data ingestion with multi-timeframe generation...")
            if not await self.run_agent('data_ingestion', **kwargs):
                logger.error("[ERROR] Data ingestion failed")
                return False

            # Step 2: Feature engineering for all timeframes
            logger.info("[STEP 2] Multi-timeframe feature engineering...")
            if not await self.run_agent('feature_engineering', **kwargs):
                logger.error("[ERROR] Feature engineering failed")
                return False

            # Step 3: Start multi-timeframe market monitoring
            logger.info("[STEP 3] Starting multi-timeframe market monitoring...")
            monitoring_task = asyncio.create_task(self.run_agent('market_monitoring', **kwargs))

            # Step 4: Generate strategies based on multi-timeframe analysis
            logger.info("[STEP 4] Multi-timeframe strategy generation...")
            if not await self.run_agent('strategy_generation', **kwargs):
                logger.error("[ERROR] Strategy generation failed")
                return False

            # Step 5: Backtest strategies across timeframes
            logger.info("[STEP 5] Multi-timeframe backtesting...")
            if not await self.run_agent('backtesting', **kwargs):
                logger.error("[ERROR] Backtesting failed")
                return False

            # Step 6: Train AI models on multi-timeframe data
            logger.info("[STEP 6] AI training on multi-timeframe features...")
            if not await self.run_agent('ai_training', **kwargs):
                logger.error("[ERROR] AI training failed")
                return False

            # Step 7: Generate multi-timeframe signals
            logger.info("[STEP 7] Multi-timeframe signal generation...")
            signal_task = asyncio.create_task(self.run_agent('signal_generation', **kwargs))

            # Step 8: Performance analysis across timeframes
            logger.info("[STEP 8] Multi-timeframe performance analysis...")
            if not await self.run_agent('performance_analysis', **kwargs):
                logger.error("[ERROR] Performance analysis failed")
                return False

            # Wait for monitoring and signal generation to complete
            await asyncio.gather(monitoring_task, signal_task)

            logger.info("[SUCCESS] Multi-timeframe analysis workflow completed")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Multi-timeframe analysis workflow failed: {e}")
            return False

    async def cleanup(self):
        """Cleanup all agents and resources"""
        try:
            logger.info("[CLEANUP] Shutting down all agents...")
            
            for agent_name, agent in self.agents.items():
                try:
                    if hasattr(agent, 'cleanup'):
                        await agent.cleanup()
                    logger.info(f"[CLEANUP] {agent_name} agent cleaned up")
                except Exception as e:
                    logger.error(f"[ERROR] Error cleaning up {agent_name}: {e}")
            
            self.agents.clear()

            # Cleanup system status monitor
            if self.system_status_monitor:
                try:
                    await self.system_status_monitor.cleanup()
                    logger.info("[CLEANUP] System status monitor cleaned up")
                except Exception as e:
                    logger.error(f"[ERROR] Error cleaning up system status monitor: {e}")

            # Save final virtual account state if in paper mode
            if self.trading_mode == 'paper' and self.virtual_account:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                self.virtual_account.save_state(f'data/paper_trading/final_account_state_{timestamp}.json')
                logger.info(f"[PAPER] Final account balance: Rs. {self.virtual_account.get_balance():,.2f}")
                logger.info(f"[PAPER] Total P&L: Rs. {self.virtual_account.get_pnl():,.2f}")
            
            logger.info("[CLEANUP] All agents cleaned up successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Error during cleanup: {e}")

async def main():
    """Main entry point for the options trading system"""
    parser = argparse.ArgumentParser(
        description='[INIT] Nifty & Bank Nifty Options Trading System - Centralized Control Center',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
[TARGET] INDIVIDUAL AGENT EXAMPLES:
  python main.py --agent data_ingestion
  python main.py --agent feature_engineering
  python main.py --agent ai_training --config custom_config.yaml
  python main.py --agent backtesting --demo
  python main.py --agent signal_generation --paper
  python main.py --agent market_monitoring --real
  python main.py --agent risk_management
  python main.py --agent execution --paper
  python main.py --agent performance_analysis
  python main.py --agent llm_interface
  python main.py --agent strategy_evolution

[WORKFLOW] COMPLETE WORKFLOW EXAMPLES:
  python main.py --workflow full_pipeline
  python main.py --workflow live_trading --paper
  python main.py --workflow live_trading --real
  python main.py --workflow training_pipeline                    # Incremental training (default)
  python main.py --workflow training_pipeline --no_incremental   # Batch training
  python main.py --workflow data_pipeline
  python main.py --workflow strategy_development
  python main.py --workflow options_research

[TRADING MODES]:
  --demo    : Demo mode with simulated data (default)
  --paper   : Paper trading with virtual Rs. 1,00,000 balance
  --real    : Real trading with SmartAPI integration

[STATUS] MONITORING:
  python main.py --agent market_monitoring --config config/options_market_monitoring_config.yaml
  python main.py --workflow live_trading --monitor --paper
        """
    )

    # Agent execution
    parser.add_argument(
        '--agent',
        choices=[
            'data_ingestion', 'feature_engineering', 'strategy_generation',
            'backtesting', 'ai_training', 'market_monitoring', 'signal_generation',
            'risk_management', 'execution', 'performance_analysis',
            'llm_interface', 'strategy_evolution'
        ],
        help='Run a specific options trading agent'
    )

    # Workflow execution
    parser.add_argument(
        '--workflow',
        choices=[
            'full_pipeline', 'training_pipeline', 'live_trading',
            'data_pipeline', 'strategy_development', 'options_research',
            'multi_timeframe_analysis'
        ],
        help='Run a complete options trading workflow'
    )

    # Configuration
    parser.add_argument(
        '--config',
        type=str,
        help='Path to configuration file'
    )

    # Demo mode
    parser.add_argument(
        '--demo',
        action='store_true',
        help='Run in demo mode (default)'
    )

    # Paper trading mode
    parser.add_argument(
        '--paper',
        action='store_true',
        help='Run in paper trading mode with virtual Rs. 1,00,000 balance'
    )

    # Real trading mode
    parser.add_argument(
        '--real',
        action='store_true',
        help='Run in real trading mode with live SmartAPI integration'
    )

    # Monitoring
    parser.add_argument(
        '--monitor',
        action='store_true',
        help='Enable monitoring mode'
    )

    # Skip historical data download
    parser.add_argument(
        '--skip_historical',
        action='store_true',
        help='Skip historical data download (use existing data)'
    )

    # Incremental training control
    parser.add_argument(
        '--no_incremental',
        action='store_true',
        help='Disable incremental training (use batch training instead)'
    )

    args = parser.parse_args()

    # Create orchestrator
    orchestrator = OptionsSystemOrchestrator()

    # Determine trading mode
    if args.real:
        orchestrator.set_trading_mode('real')
    elif args.paper:
        orchestrator.set_trading_mode('paper')
    elif args.agent == 'strategy_evolution' and not args.demo:
        orchestrator.set_trading_mode('real')
        logger.info("[MODE] Defaulting strategy_evolution agent to REAL mode.")
    else:
        orchestrator.set_trading_mode('demo')

    try:
        # Agent execution
        if args.agent:
            logger.info(f"[AGENT] Running {args.agent} agent...")
            success = await orchestrator.run_agent(
                args.agent,
                config_path=args.config,
                demo=args.demo,
                monitor=args.monitor
            )

            if success:
                print(f"[SUCCESS] {args.agent} agent completed successfully")
            else:
                print(f"[ERROR] {args.agent} agent failed")
                sys.exit(1)

            return

        # Workflow execution
        if args.workflow:
            logger.info(f"[WORKFLOW] Running {args.workflow} workflow...")

            # Determine incremental training setting
            incremental_training = not args.no_incremental  # Default to True unless --no_incremental is specified

            success = await orchestrator.run_workflow(
                args.workflow,
                monitor=args.monitor,
                skip_historical=args.skip_historical,
                incremental=incremental_training
            )

            if success:
                print(f"[SUCCESS] {args.workflow} workflow completed successfully")
            else:
                print(f"[ERROR] {args.workflow} workflow failed")
                sys.exit(1)

            return

        # If no specific command, show help
        parser.print_help()

    except KeyboardInterrupt:
        logger.info("[EXIT] Options trading system interrupted by user")
    except Exception as e:
        logger.error(f"[ERROR] Fatal error: {e}")
        sys.exit(1)
    finally:
        await orchestrator.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
